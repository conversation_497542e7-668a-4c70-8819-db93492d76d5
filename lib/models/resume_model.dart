import 'package:equatable/equatable.dart';
import 'work_experience_model.dart';
import 'education_model.dart';
import 'skill_model.dart';

class ResumeModel extends Equatable {
  final String id;
  final String userId;
  final String name;
  final PersonalInfo personalInfo;
  final String summary;
  final List<WorkExperience> workExperience;
  final List<Education> education;
  final List<Skill> skills;
  final List<String> achievements;
  final String style;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;
  final String? pdfUrl;
  final String? shareUrl;

  const ResumeModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.personalInfo,
    required this.summary,
    required this.workExperience,
    required this.education,
    required this.skills,
    required this.achievements,
    required this.style,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
    this.pdfUrl,
    this.shareUrl,
  });

  factory ResumeModel.fromJson(Map<String, dynamic> json) {
    return ResumeModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      name: json['name'] ?? '',
      personalInfo: PersonalInfo.fromJson(json['personalInfo'] ?? {}),
      summary: json['summary'] ?? '',
      workExperience: (json['workExperience'] as List?)
          ?.map((e) => WorkExperience.fromJson(e))
          .toList() ?? [],
      education: (json['education'] as List?)
          ?.map((e) => Education.fromJson(e))
          .toList() ?? [],
      skills: (json['skills'] as List?)
          ?.map((e) => Skill.fromJson(e))
          .toList() ?? [],
      achievements: List<String>.from(json['achievements'] ?? []),
      style: json['style'] ?? 'professional',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      version: json['version'] ?? 1,
      pdfUrl: json['pdfUrl'],
      shareUrl: json['shareUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'personalInfo': personalInfo.toJson(),
      'summary': summary,
      'workExperience': workExperience.map((e) => e.toJson()).toList(),
      'education': education.map((e) => e.toJson()).toList(),
      'skills': skills.map((e) => e.toJson()).toList(),
      'achievements': achievements,
      'style': style,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'version': version,
      'pdfUrl': pdfUrl,
      'shareUrl': shareUrl,
    };
  }

  ResumeModel copyWith({
    String? id,
    String? userId,
    String? name,
    PersonalInfo? personalInfo,
    String? summary,
    List<WorkExperience>? workExperience,
    List<Education>? education,
    List<Skill>? skills,
    List<String>? achievements,
    String? style,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? version,
    String? pdfUrl,
    String? shareUrl,
  }) {
    return ResumeModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      personalInfo: personalInfo ?? this.personalInfo,
      summary: summary ?? this.summary,
      workExperience: workExperience ?? this.workExperience,
      education: education ?? this.education,
      skills: skills ?? this.skills,
      achievements: achievements ?? this.achievements,
      style: style ?? this.style,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      version: version ?? this.version,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      shareUrl: shareUrl ?? this.shareUrl,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        personalInfo,
        summary,
        workExperience,
        education,
        skills,
        achievements,
        style,
        createdAt,
        updatedAt,
        version,
        pdfUrl,
        shareUrl,
      ];
}

class PersonalInfo extends Equatable {
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String? address;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? country;
  final String? linkedIn;
  final String? website;
  final String? github;

  const PersonalInfo({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    this.address,
    this.city,
    this.state,
    this.zipCode,
    this.country,
    this.linkedIn,
    this.website,
    this.github,
  });

  factory PersonalInfo.fromJson(Map<String, dynamic> json) {
    return PersonalInfo(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'],
      city: json['city'],
      state: json['state'],
      zipCode: json['zipCode'],
      country: json['country'],
      linkedIn: json['linkedIn'],
      website: json['website'],
      github: json['github'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'address': address,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
      'linkedIn': linkedIn,
      'website': website,
      'github': github,
    };
  }

  String get fullName => '$firstName $lastName';

  PersonalInfo copyWith({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? linkedIn,
    String? website,
    String? github,
  }) {
    return PersonalInfo(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      linkedIn: linkedIn ?? this.linkedIn,
      website: website ?? this.website,
      github: github ?? this.github,
    );
  }

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        email,
        phone,
        address,
        city,
        state,
        zipCode,
        country,
        linkedIn,
        website,
        github,
      ];
}

enum ResumeStyle {
  professional,
  creative,
  minimalist,
  modern,
}

extension ResumeStyleExtension on ResumeStyle {
  String get displayName {
    switch (this) {
      case ResumeStyle.professional:
        return 'Professional';
      case ResumeStyle.creative:
        return 'Creative';
      case ResumeStyle.minimalist:
        return 'Minimalist';
      case ResumeStyle.modern:
        return 'Modern';
    }
  }

  String get description {
    switch (this) {
      case ResumeStyle.professional:
        return 'Clean and traditional format suitable for corporate environments';
      case ResumeStyle.creative:
        return 'Eye-catching design perfect for creative industries';
      case ResumeStyle.minimalist:
        return 'Simple and elegant with focus on content';
      case ResumeStyle.modern:
        return 'Contemporary design with modern typography';
    }
  }
}
