import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String? displayName;
  final String? photoUrl;
  final String? phoneNumber;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final UserPreferences preferences;
  final List<String> resumeIds;
  final bool isEmailVerified;
  final String? industry;
  final String? jobTitle;
  final int experienceYears;

  const UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    this.phoneNumber,
    required this.createdAt,
    required this.lastLoginAt,
    required this.preferences,
    this.resumeIds = const [],
    this.isEmailVerified = false,
    this.industry,
    this.jobTitle,
    this.experienceYears = 0,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      displayName: json['displayName'],
      photoUrl: json['photoUrl'],
      phoneNumber: json['phoneNumber'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] ?? DateTime.now().toIso8601String()),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
      resumeIds: List<String>.from(json['resumeIds'] ?? []),
      isEmailVerified: json['isEmailVerified'] ?? false,
      industry: json['industry'],
      jobTitle: json['jobTitle'],
      experienceYears: json['experienceYears'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'phoneNumber': phoneNumber,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'preferences': preferences.toJson(),
      'resumeIds': resumeIds,
      'isEmailVerified': isEmailVerified,
      'industry': industry,
      'jobTitle': jobTitle,
      'experienceYears': experienceYears,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    UserPreferences? preferences,
    List<String>? resumeIds,
    bool? isEmailVerified,
    String? industry,
    String? jobTitle,
    int? experienceYears,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      resumeIds: resumeIds ?? this.resumeIds,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      industry: industry ?? this.industry,
      jobTitle: jobTitle ?? this.jobTitle,
      experienceYears: experienceYears ?? this.experienceYears,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        photoUrl,
        phoneNumber,
        createdAt,
        lastLoginAt,
        preferences,
        resumeIds,
        isEmailVerified,
        industry,
        jobTitle,
        experienceYears,
      ];
}

class UserPreferences extends Equatable {
  final String defaultResumeStyle;
  final bool enableNotifications;
  final bool autoSave;
  final String language;
  final String dateFormat;
  final bool darkMode;
  final List<String> favoriteTemplates;

  const UserPreferences({
    this.defaultResumeStyle = 'professional',
    this.enableNotifications = true,
    this.autoSave = true,
    this.language = 'en',
    this.dateFormat = 'MM/dd/yyyy',
    this.darkMode = false,
    this.favoriteTemplates = const [],
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      defaultResumeStyle: json['defaultResumeStyle'] ?? 'professional',
      enableNotifications: json['enableNotifications'] ?? true,
      autoSave: json['autoSave'] ?? true,
      language: json['language'] ?? 'en',
      dateFormat: json['dateFormat'] ?? 'MM/dd/yyyy',
      darkMode: json['darkMode'] ?? false,
      favoriteTemplates: List<String>.from(json['favoriteTemplates'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'defaultResumeStyle': defaultResumeStyle,
      'enableNotifications': enableNotifications,
      'autoSave': autoSave,
      'language': language,
      'dateFormat': dateFormat,
      'darkMode': darkMode,
      'favoriteTemplates': favoriteTemplates,
    };
  }

  UserPreferences copyWith({
    String? defaultResumeStyle,
    bool? enableNotifications,
    bool? autoSave,
    String? language,
    String? dateFormat,
    bool? darkMode,
    List<String>? favoriteTemplates,
  }) {
    return UserPreferences(
      defaultResumeStyle: defaultResumeStyle ?? this.defaultResumeStyle,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      autoSave: autoSave ?? this.autoSave,
      language: language ?? this.language,
      dateFormat: dateFormat ?? this.dateFormat,
      darkMode: darkMode ?? this.darkMode,
      favoriteTemplates: favoriteTemplates ?? this.favoriteTemplates,
    );
  }

  @override
  List<Object?> get props => [
        defaultResumeStyle,
        enableNotifications,
        autoSave,
        language,
        dateFormat,
        darkMode,
        favoriteTemplates,
      ];
}

enum AuthProvider {
  email,
  google,
  apple,
}

extension AuthProviderExtension on AuthProvider {
  String get displayName {
    switch (this) {
      case AuthProvider.email:
        return 'Email';
      case AuthProvider.google:
        return 'Google';
      case AuthProvider.apple:
        return 'Apple';
    }
  }

  String get iconPath {
    switch (this) {
      case AuthProvider.email:
        return 'assets/icons/email.svg';
      case AuthProvider.google:
        return 'assets/icons/google.svg';
      case AuthProvider.apple:
        return 'assets/icons/apple.svg';
    }
  }
}
