import 'package:equatable/equatable.dart';

class Skill extends Equatable {
  final String id;
  final String name;
  final SkillCategory category;
  final SkillLevel level;
  final int? yearsOfExperience;
  final bool isEndorsed;
  final List<String> endorsements;

  const Skill({
    required this.id,
    required this.name,
    required this.category,
    required this.level,
    this.yearsOfExperience,
    this.isEndorsed = false,
    this.endorsements = const [],
  });

  factory Skill.fromJson(Map<String, dynamic> json) {
    return Skill(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      category: SkillCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => SkillCategory.other,
      ),
      level: SkillLevel.values.firstWhere(
        (e) => e.toString().split('.').last == json['level'],
        orElse: () => SkillLevel.beginner,
      ),
      yearsOfExperience: json['yearsOfExperience'],
      isEndorsed: json['isEndorsed'] ?? false,
      endorsements: List<String>.from(json['endorsements'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category.toString().split('.').last,
      'level': level.toString().split('.').last,
      'yearsOfExperience': yearsOfExperience,
      'isEndorsed': isEndorsed,
      'endorsements': endorsements,
    };
  }

  Skill copyWith({
    String? id,
    String? name,
    SkillCategory? category,
    SkillLevel? level,
    int? yearsOfExperience,
    bool? isEndorsed,
    List<String>? endorsements,
  }) {
    return Skill(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      level: level ?? this.level,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      isEndorsed: isEndorsed ?? this.isEndorsed,
      endorsements: endorsements ?? this.endorsements,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        category,
        level,
        yearsOfExperience,
        isEndorsed,
        endorsements,
      ];
}

enum SkillCategory {
  technical,
  programming,
  software,
  language,
  soft,
  design,
  marketing,
  management,
  finance,
  other,
}

extension SkillCategoryExtension on SkillCategory {
  String get displayName {
    switch (this) {
      case SkillCategory.technical:
        return 'Technical Skills';
      case SkillCategory.programming:
        return 'Programming Languages';
      case SkillCategory.software:
        return 'Software & Tools';
      case SkillCategory.language:
        return 'Languages';
      case SkillCategory.soft:
        return 'Soft Skills';
      case SkillCategory.design:
        return 'Design';
      case SkillCategory.marketing:
        return 'Marketing';
      case SkillCategory.management:
        return 'Management';
      case SkillCategory.finance:
        return 'Finance';
      case SkillCategory.other:
        return 'Other';
    }
  }

  List<String> get commonSkills {
    switch (this) {
      case SkillCategory.technical:
        return [
          'Data Analysis',
          'Database Management',
          'System Administration',
          'Network Security',
          'Cloud Computing',
          'DevOps',
          'API Development',
          'Testing & QA'
        ];
      case SkillCategory.programming:
        return [
          'JavaScript',
          'Python',
          'Java',
          'C++',
          'C#',
          'Swift',
          'Kotlin',
          'Go',
          'Rust',
          'TypeScript',
          'PHP',
          'Ruby',
          'Dart',
          'Flutter'
        ];
      case SkillCategory.software:
        return [
          'Microsoft Office',
          'Adobe Creative Suite',
          'Figma',
          'Sketch',
          'AutoCAD',
          'Salesforce',
          'Jira',
          'Git',
          'Docker',
          'Kubernetes'
        ];
      case SkillCategory.language:
        return [
          'English',
          'Spanish',
          'French',
          'German',
          'Chinese',
          'Japanese',
          'Arabic',
          'Portuguese',
          'Russian',
          'Italian'
        ];
      case SkillCategory.soft:
        return [
          'Leadership',
          'Communication',
          'Problem Solving',
          'Team Collaboration',
          'Time Management',
          'Critical Thinking',
          'Adaptability',
          'Creativity',
          'Public Speaking',
          'Negotiation'
        ];
      case SkillCategory.design:
        return [
          'UI/UX Design',
          'Graphic Design',
          'Web Design',
          'Brand Design',
          'Motion Graphics',
          'Typography',
          'Color Theory',
          'Wireframing',
          'Prototyping'
        ];
      case SkillCategory.marketing:
        return [
          'Digital Marketing',
          'SEO/SEM',
          'Social Media Marketing',
          'Content Marketing',
          'Email Marketing',
          'Analytics',
          'Brand Management',
          'Market Research'
        ];
      case SkillCategory.management:
        return [
          'Project Management',
          'Team Leadership',
          'Strategic Planning',
          'Budget Management',
          'Risk Management',
          'Change Management',
          'Performance Management',
          'Stakeholder Management'
        ];
      case SkillCategory.finance:
        return [
          'Financial Analysis',
          'Accounting',
          'Budgeting',
          'Investment Analysis',
          'Risk Assessment',
          'Financial Modeling',
          'Tax Preparation',
          'Auditing'
        ];
      case SkillCategory.other:
        return [];
    }
  }
}

enum SkillLevel {
  beginner,
  intermediate,
  advanced,
  expert,
}

extension SkillLevelExtension on SkillLevel {
  String get displayName {
    switch (this) {
      case SkillLevel.beginner:
        return 'Beginner';
      case SkillLevel.intermediate:
        return 'Intermediate';
      case SkillLevel.advanced:
        return 'Advanced';
      case SkillLevel.expert:
        return 'Expert';
    }
  }

  String get description {
    switch (this) {
      case SkillLevel.beginner:
        return 'Basic understanding and limited experience';
      case SkillLevel.intermediate:
        return 'Good working knowledge with some experience';
      case SkillLevel.advanced:
        return 'Strong expertise with extensive experience';
      case SkillLevel.expert:
        return 'Deep expertise and ability to teach others';
    }
  }

  int get percentage {
    switch (this) {
      case SkillLevel.beginner:
        return 25;
      case SkillLevel.intermediate:
        return 50;
      case SkillLevel.advanced:
        return 75;
      case SkillLevel.expert:
        return 100;
    }
  }
}
