import 'package:equatable/equatable.dart';

class WorkExperience extends Equatable {
  final String id;
  final String jobTitle;
  final String company;
  final String? location;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isCurrentJob;
  final String description;
  final List<String> responsibilities;
  final List<String> achievements;
  final String? industry;
  final String? employmentType; // Full-time, Part-time, Contract, Internship

  const WorkExperience({
    required this.id,
    required this.jobTitle,
    required this.company,
    this.location,
    required this.startDate,
    this.endDate,
    required this.isCurrentJob,
    required this.description,
    required this.responsibilities,
    required this.achievements,
    this.industry,
    this.employmentType,
  });

  factory WorkExperience.fromJson(Map<String, dynamic> json) {
    return WorkExperience(
      id: json['id'] ?? '',
      jobTitle: json['jobTitle'] ?? '',
      company: json['company'] ?? '',
      location: json['location'],
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isCurrentJob: json['isCurrentJob'] ?? false,
      description: json['description'] ?? '',
      responsibilities: List<String>.from(json['responsibilities'] ?? []),
      achievements: List<String>.from(json['achievements'] ?? []),
      industry: json['industry'],
      employmentType: json['employmentType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'jobTitle': jobTitle,
      'company': company,
      'location': location,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isCurrentJob': isCurrentJob,
      'description': description,
      'responsibilities': responsibilities,
      'achievements': achievements,
      'industry': industry,
      'employmentType': employmentType,
    };
  }

  WorkExperience copyWith({
    String? id,
    String? jobTitle,
    String? company,
    String? location,
    DateTime? startDate,
    DateTime? endDate,
    bool? isCurrentJob,
    String? description,
    List<String>? responsibilities,
    List<String>? achievements,
    String? industry,
    String? employmentType,
  }) {
    return WorkExperience(
      id: id ?? this.id,
      jobTitle: jobTitle ?? this.jobTitle,
      company: company ?? this.company,
      location: location ?? this.location,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isCurrentJob: isCurrentJob ?? this.isCurrentJob,
      description: description ?? this.description,
      responsibilities: responsibilities ?? this.responsibilities,
      achievements: achievements ?? this.achievements,
      industry: industry ?? this.industry,
      employmentType: employmentType ?? this.employmentType,
    );
  }

  String get duration {
    final start = startDate;
    final end = endDate ?? DateTime.now();
    
    final years = end.year - start.year;
    final months = end.month - start.month;
    
    if (years == 0) {
      return months == 1 ? '1 month' : '$months months';
    } else if (months == 0) {
      return years == 1 ? '1 year' : '$years years';
    } else {
      final yearText = years == 1 ? '1 year' : '$years years';
      final monthText = months == 1 ? '1 month' : '$months months';
      return '$yearText $monthText';
    }
  }

  String get dateRange {
    final startMonth = _getMonthName(startDate.month);
    final startYear = startDate.year;
    
    if (isCurrentJob) {
      return '$startMonth $startYear - Present';
    } else if (endDate != null) {
      final endMonth = _getMonthName(endDate!.month);
      final endYear = endDate!.year;
      return '$startMonth $startYear - $endMonth $endYear';
    } else {
      return '$startMonth $startYear - Present';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  @override
  List<Object?> get props => [
        id,
        jobTitle,
        company,
        location,
        startDate,
        endDate,
        isCurrentJob,
        description,
        responsibilities,
        achievements,
        industry,
        employmentType,
      ];
}

enum EmploymentType {
  fullTime,
  partTime,
  contract,
  internship,
  freelance,
  volunteer,
}

extension EmploymentTypeExtension on EmploymentType {
  String get displayName {
    switch (this) {
      case EmploymentType.fullTime:
        return 'Full-time';
      case EmploymentType.partTime:
        return 'Part-time';
      case EmploymentType.contract:
        return 'Contract';
      case EmploymentType.internship:
        return 'Internship';
      case EmploymentType.freelance:
        return 'Freelance';
      case EmploymentType.volunteer:
        return 'Volunteer';
    }
  }
}
