import 'package:equatable/equatable.dart';

class Education extends Equatable {
  final String id;
  final String institution;
  final String degree;
  final String? fieldOfStudy;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isCurrentlyStudying;
  final double? gpa;
  final String? description;
  final List<String> coursework;
  final List<String> achievements;
  final String? location;

  const Education({
    required this.id,
    required this.institution,
    required this.degree,
    this.fieldOfStudy,
    required this.startDate,
    this.endDate,
    required this.isCurrentlyStudying,
    this.gpa,
    this.description,
    required this.coursework,
    required this.achievements,
    this.location,
  });

  factory Education.fromJson(Map<String, dynamic> json) {
    return Education(
      id: json['id'] ?? '',
      institution: json['institution'] ?? '',
      degree: json['degree'] ?? '',
      fieldOfStudy: json['fieldOfStudy'],
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isCurrentlyStudying: json['isCurrentlyStudying'] ?? false,
      gpa: json['gpa']?.toDouble(),
      description: json['description'],
      coursework: List<String>.from(json['coursework'] ?? []),
      achievements: List<String>.from(json['achievements'] ?? []),
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'institution': institution,
      'degree': degree,
      'fieldOfStudy': fieldOfStudy,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isCurrentlyStudying': isCurrentlyStudying,
      'gpa': gpa,
      'description': description,
      'coursework': coursework,
      'achievements': achievements,
      'location': location,
    };
  }

  Education copyWith({
    String? id,
    String? institution,
    String? degree,
    String? fieldOfStudy,
    DateTime? startDate,
    DateTime? endDate,
    bool? isCurrentlyStudying,
    double? gpa,
    String? description,
    List<String>? coursework,
    List<String>? achievements,
    String? location,
  }) {
    return Education(
      id: id ?? this.id,
      institution: institution ?? this.institution,
      degree: degree ?? this.degree,
      fieldOfStudy: fieldOfStudy ?? this.fieldOfStudy,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isCurrentlyStudying: isCurrentlyStudying ?? this.isCurrentlyStudying,
      gpa: gpa ?? this.gpa,
      description: description ?? this.description,
      coursework: coursework ?? this.coursework,
      achievements: achievements ?? this.achievements,
      location: location ?? this.location,
    );
  }

  String get dateRange {
    final startYear = startDate.year;
    
    if (isCurrentlyStudying) {
      return '$startYear - Present';
    } else if (endDate != null) {
      final endYear = endDate!.year;
      return '$startYear - $endYear';
    } else {
      return '$startYear - Present';
    }
  }

  String get fullDegree {
    if (fieldOfStudy != null && fieldOfStudy!.isNotEmpty) {
      return '$degree in $fieldOfStudy';
    }
    return degree;
  }

  String get gpaDisplay {
    if (gpa != null) {
      return 'GPA: ${gpa!.toStringAsFixed(2)}';
    }
    return '';
  }

  @override
  List<Object?> get props => [
        id,
        institution,
        degree,
        fieldOfStudy,
        startDate,
        endDate,
        isCurrentlyStudying,
        gpa,
        description,
        coursework,
        achievements,
        location,
      ];
}

enum DegreeType {
  highSchool,
  associate,
  bachelor,
  master,
  doctorate,
  certificate,
  diploma,
}

extension DegreeTypeExtension on DegreeType {
  String get displayName {
    switch (this) {
      case DegreeType.highSchool:
        return 'High School Diploma';
      case DegreeType.associate:
        return 'Associate Degree';
      case DegreeType.bachelor:
        return 'Bachelor\'s Degree';
      case DegreeType.master:
        return 'Master\'s Degree';
      case DegreeType.doctorate:
        return 'Doctorate';
      case DegreeType.certificate:
        return 'Certificate';
      case DegreeType.diploma:
        return 'Diploma';
    }
  }

  List<String> get commonTitles {
    switch (this) {
      case DegreeType.highSchool:
        return ['High School Diploma', 'GED'];
      case DegreeType.associate:
        return ['Associate of Arts (AA)', 'Associate of Science (AS)', 'Associate of Applied Science (AAS)'];
      case DegreeType.bachelor:
        return ['Bachelor of Arts (BA)', 'Bachelor of Science (BS)', 'Bachelor of Engineering (BE)', 'Bachelor of Technology (BTech)'];
      case DegreeType.master:
        return ['Master of Arts (MA)', 'Master of Science (MS)', 'Master of Business Administration (MBA)', 'Master of Engineering (ME)'];
      case DegreeType.doctorate:
        return ['Doctor of Philosophy (PhD)', 'Doctor of Medicine (MD)', 'Doctor of Education (EdD)', 'Doctor of Jurisprudence (JD)'];
      case DegreeType.certificate:
        return ['Professional Certificate', 'Technical Certificate', 'Industry Certificate'];
      case DegreeType.diploma:
        return ['Professional Diploma', 'Technical Diploma', 'Graduate Diploma'];
    }
  }
}
