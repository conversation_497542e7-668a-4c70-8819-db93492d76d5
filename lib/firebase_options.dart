// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD8HHn257uhQ_fMihsNRQLLLTvDvtn-Zig',
    appId: '1:992689379497:ios:05951e291cd5f2077371b8',
    messagingSenderId: '992689379497',
    projectId: 'resume-d24cb',
    storageBucket: 'resume-d24cb.firebasestorage.app',
    androidClientId: '992689379497-iurms652ma7u7o1dqk16fu8me5e0apn9.apps.googleusercontent.com',
    iosClientId: '992689379497-n7tlmftp7gq6ia4us94hrvn9uq9dtn8v.apps.googleusercontent.com',
    iosBundleId: 'com.appgenie.resumeBuilder',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyD8HHn257uhQ_fMihsNRQLLLTvDvtn-Zig',
    appId: '1:992689379497:ios:05951e291cd5f2077371b8',
    messagingSenderId: '992689379497',
    projectId: 'resume-d24cb',
    storageBucket: 'resume-d24cb.firebasestorage.app',
    androidClientId: '992689379497-iurms652ma7u7o1dqk16fu8me5e0apn9.apps.googleusercontent.com',
    iosClientId: '992689379497-n7tlmftp7gq6ia4us94hrvn9uq9dtn8v.apps.googleusercontent.com',
    iosBundleId: 'com.appgenie.resumeBuilder',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA9W-GhVFtqkGe5VY15qpOMwz-8aoQTuYo',
    appId: '1:992689379497:web:5d8a0938c65590157371b8',
    messagingSenderId: '992689379497',
    projectId: 'resume-d24cb',
    authDomain: 'resume-d24cb.firebaseapp.com',
    storageBucket: 'resume-d24cb.firebasestorage.app',
    measurementId: 'G-H8MRKRDY4P',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyA9W-GhVFtqkGe5VY15qpOMwz-8aoQTuYo',
    appId: '1:992689379497:web:97bc54991e19348b7371b8',
    messagingSenderId: '992689379497',
    projectId: 'resume-d24cb',
    authDomain: 'resume-d24cb.firebaseapp.com',
    storageBucket: 'resume-d24cb.firebasestorage.app',
    measurementId: 'G-62WXY4WHWJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC9ZCTydBu5eBon_q5P3fOchxYgYElEdr8',
    appId: '1:992689379497:android:fcdfac079e2dd3017371b8',
    messagingSenderId: '992689379497',
    projectId: 'resume-d24cb',
    storageBucket: 'resume-d24cb.firebasestorage.app',
  );

}