import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/design_tokens.dart';

enum ModernButtonVariant {
  primary,
  secondary,
  tertiary,
  destructive,
  success,
}

enum ModernButtonSize {
  small,
  medium,
  large,
}

class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ModernButtonVariant variant;
  final ModernButtonSize size;
  final Widget? icon;
  final bool isLoading;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = ModernButtonVariant.primary,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.padding,
    this.borderRadius,
  });

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignTokens.durationFast,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 4.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  EdgeInsetsGeometry _getPadding() {
    if (widget.padding != null) return widget.padding!;
    
    switch (widget.size) {
      case ModernButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: DesignTokens.spaceLg,
          vertical: DesignTokens.spaceSm,
        );
      case ModernButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: DesignTokens.space2xl,
          vertical: DesignTokens.spaceLg,
        );
      case ModernButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: DesignTokens.space3xl,
          vertical: DesignTokens.spaceXl,
        );
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 14;
      case ModernButtonSize.medium:
        return 16;
      case ModernButtonSize.large:
        return 18;
    }
  }

  BorderRadius _getBorderRadius() {
    if (widget.borderRadius != null) return widget.borderRadius!;
    
    switch (widget.size) {
      case ModernButtonSize.small:
        return BorderRadius.circular(DesignTokens.radiusMd);
      case ModernButtonSize.medium:
        return BorderRadius.circular(DesignTokens.radiusLg);
      case ModernButtonSize.large:
        return BorderRadius.circular(DesignTokens.radiusXl);
    }
  }

  ButtonStyle _getButtonStyle() {
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    
    switch (widget.variant) {
      case ModernButtonVariant.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled 
              ? DesignTokens.primaryBlue 
              : DesignTokens.neutral300,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: _getBorderRadius()),
          padding: _getPadding(),
        );
      
      case ModernButtonVariant.secondary:
        return OutlinedButton.styleFrom(
          foregroundColor: isEnabled 
              ? DesignTokens.primaryBlue 
              : DesignTokens.neutral400,
          side: BorderSide(
            color: isEnabled 
                ? DesignTokens.primaryBlue.withValues(alpha: 0.3) 
                : DesignTokens.neutral300,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(borderRadius: _getBorderRadius()),
          padding: _getPadding(),
        );
      
      case ModernButtonVariant.tertiary:
        return TextButton.styleFrom(
          foregroundColor: isEnabled 
              ? DesignTokens.primaryBlue 
              : DesignTokens.neutral400,
          shape: RoundedRectangleBorder(borderRadius: _getBorderRadius()),
          padding: _getPadding(),
        );
      
      case ModernButtonVariant.destructive:
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled 
              ? DesignTokens.accentRed 
              : DesignTokens.neutral300,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: _getBorderRadius()),
          padding: _getPadding(),
        );
      
      case ModernButtonVariant.success:
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled 
              ? DesignTokens.accentGreen 
              : DesignTokens.neutral300,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: _getBorderRadius()),
          padding: _getPadding(),
        );
    }
  }

  Widget _buildButton() {
    final buttonStyle = _getButtonStyle();
    final textStyle = TextStyle(
      fontSize: _getFontSize(),
      fontWeight: FontWeight.w600,
      letterSpacing: -0.1,
    );

    Widget buttonContent = Row(
      mainAxisSize: widget.isFullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.isLoading) ...[
          SizedBox(
            width: _getFontSize(),
            height: _getFontSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.variant == ModernButtonVariant.secondary ||
                widget.variant == ModernButtonVariant.tertiary
                    ? DesignTokens.primaryBlue
                    : Colors.white,
              ),
            ),
          ),
          const SizedBox(width: DesignTokens.spaceSm),
        ] else if (widget.icon != null) ...[
          widget.icon!,
          const SizedBox(width: DesignTokens.spaceSm),
        ],
        Text(widget.text, style: textStyle),
      ],
    );

    switch (widget.variant) {
      case ModernButtonVariant.primary:
      case ModernButtonVariant.destructive:
      case ModernButtonVariant.success:
        return ElevatedButton(
          onPressed: widget.isLoading ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonContent,
        );
      
      case ModernButtonVariant.secondary:
        return OutlinedButton(
          onPressed: widget.isLoading ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonContent,
        );
      
      case ModernButtonVariant.tertiary:
        return TextButton(
          onPressed: widget.isLoading ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonContent,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return GestureDetector(
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.isFullWidth ? double.infinity : null,
              decoration: BoxDecoration(
                borderRadius: _getBorderRadius(),
                boxShadow: _isPressed && widget.variant == ModernButtonVariant.primary
                    ? [
                        BoxShadow(
                          color: DesignTokens.primaryBlue.withValues(alpha: 0.3),
                          blurRadius: _elevationAnimation.value * 2,
                          offset: Offset(0, _elevationAnimation.value),
                        ),
                      ]
                    : null,
              ),
              child: _buildButton(),
            ),
          ),
        );
      },
    );
  }
}
