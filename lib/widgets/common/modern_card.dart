import 'package:flutter/material.dart';
import '../../utils/design_tokens.dart';

enum ModernCardVariant {
  elevated,
  outlined,
  filled,
  glass,
}

class ModernCard extends StatefulWidget {
  final Widget child;
  final ModernCardVariant variant;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final List<BoxShadow>? boxShadow;
  final bool showHoverEffect;
  final bool showPressEffect;

  const ModernCard({
    super.key,
    required this.child,
    this.variant = ModernCardVariant.elevated,
    this.onTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.boxShadow,
    this.showHoverEffect = true,
    this.showPressEffect = true,
  });

  @override
  State<ModernCard> createState() => _ModernCardState();
}

class _ModernCardState extends State<ModernCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignTokens.durationFast,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null && widget.showPressEffect) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.showPressEffect) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.showPressEffect) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleHoverEnter(PointerEvent event) {
    if (widget.showHoverEffect) {
      setState(() => _isHovered = true);
    }
  }

  void _handleHoverExit(PointerEvent event) {
    if (widget.showHoverEffect) {
      setState(() => _isHovered = false);
    }
  }

  BoxDecoration _getDecoration() {
    Color backgroundColor;
    Color? borderColor;
    List<BoxShadow> boxShadow;
    double borderRadius = widget.borderRadius ?? DesignTokens.radiusXl;

    switch (widget.variant) {
      case ModernCardVariant.elevated:
        backgroundColor = widget.backgroundColor ?? Colors.white;
        borderColor = widget.borderColor;
        boxShadow = widget.boxShadow ?? 
            (_isHovered ? DesignTokens.shadowLg : DesignTokens.shadowMd);
        break;
      
      case ModernCardVariant.outlined:
        backgroundColor = widget.backgroundColor ?? Colors.white;
        borderColor = widget.borderColor ?? DesignTokens.neutral300;
        boxShadow = widget.boxShadow ?? 
            (_isHovered ? DesignTokens.shadowSm : []);
        break;
      
      case ModernCardVariant.filled:
        backgroundColor = widget.backgroundColor ?? DesignTokens.neutral50;
        borderColor = widget.borderColor;
        boxShadow = widget.boxShadow ?? [];
        break;
      
      case ModernCardVariant.glass:
        backgroundColor = widget.backgroundColor ?? 
            Colors.white.withValues(alpha: 0.8);
        borderColor = widget.borderColor ?? 
            Colors.white.withValues(alpha: 0.2);
        boxShadow = widget.boxShadow ?? DesignTokens.shadowMd;
        break;
    }

    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(borderRadius),
      border: borderColor != null 
          ? Border.all(color: borderColor, width: 1.5)
          : null,
      boxShadow: boxShadow,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return MouseRegion(
          onEnter: _handleHoverEnter,
          onExit: _handleHoverExit,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onTap: widget.onTap,
            child: Transform.scale(
              scale: widget.showPressEffect ? _scaleAnimation.value : 1.0,
              child: AnimatedContainer(
                duration: DesignTokens.durationFast,
                margin: widget.margin,
                padding: widget.padding ?? const EdgeInsets.all(DesignTokens.spaceLg),
                decoration: _getDecoration(),
                child: widget.child,
              ),
            ),
          ),
        );
      },
    );
  }
}

// Specialized card variants for common use cases
class ModernInfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;

  const ModernInfoCard({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      onTap: onTap,
      padding: padding ?? const EdgeInsets.all(DesignTokens.spaceLg),
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: DesignTokens.spaceLg),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: DesignTokens.textPrimary,
                    letterSpacing: -0.1,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: DesignTokens.spaceXs),
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: DesignTokens.textTertiary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: DesignTokens.spaceLg),
            trailing!,
          ],
        ],
      ),
    );
  }
}

class ModernActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback? onTap;

  const ModernActionCard({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      onTap: onTap,
      variant: ModernCardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(DesignTokens.spaceMd),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color,
                  color.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: DesignTokens.spaceLg),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: DesignTokens.textPrimary,
              letterSpacing: -0.1,
            ),
          ),
          const SizedBox(height: DesignTokens.spaceXs),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: DesignTokens.textTertiary,
            ),
          ),
        ],
      ),
    );
  }
}
