import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/design_tokens.dart';

class ModernTextField extends StatefulWidget {
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final AutovalidateMode? autovalidateMode;
  final bool showFloatingLabel;
  final EdgeInsetsGeometry? contentPadding;

  const ModernTextField({
    super.key,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onTap,
    this.controller,
    this.focusNode,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.autovalidateMode,
    this.showFloatingLabel = true,
    this.contentPadding,
  });

  @override
  State<ModernTextField> createState() => _ModernTextFieldState();
}

class _ModernTextFieldState extends State<ModernTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _borderColorAnimation;
  
  bool _isFocused = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignTokens.durationFast,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _borderColorAnimation = ColorTween(
      begin: DesignTokens.neutral300,
      end: DesignTokens.primaryBlue,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _hasError = widget.errorText != null;
  }

  @override
  void didUpdateWidget(ModernTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.errorText != oldWidget.errorText) {
      setState(() {
        _hasError = widget.errorText != null;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleFocusChange(bool hasFocus) {
    setState(() {
      _isFocused = hasFocus;
    });
    
    if (hasFocus) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              boxShadow: _isFocused && !_hasError
                  ? [
                      BoxShadow(
                        color: DesignTokens.primaryBlue.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Focus(
              onFocusChange: _handleFocusChange,
              child: TextFormField(
                controller: widget.controller,
                focusNode: widget.focusNode,
                obscureText: widget.obscureText,
                keyboardType: widget.keyboardType,
                textInputAction: widget.textInputAction,
                onChanged: widget.onChanged,
                onTap: widget.onTap,
                enabled: widget.enabled,
                readOnly: widget.readOnly,
                maxLines: widget.maxLines,
                minLines: widget.minLines,
                maxLength: widget.maxLength,
                inputFormatters: widget.inputFormatters,
                validator: widget.validator,
                autovalidateMode: widget.autovalidateMode,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: widget.enabled 
                      ? DesignTokens.textPrimary 
                      : DesignTokens.textTertiary,
                ),
                decoration: InputDecoration(
                  labelText: widget.showFloatingLabel ? widget.labelText : null,
                  hintText: widget.hintText,
                  helperText: widget.helperText,
                  errorText: widget.errorText,
                  prefixIcon: widget.prefixIcon != null
                      ? Padding(
                          padding: const EdgeInsets.only(left: DesignTokens.spaceMd),
                          child: widget.prefixIcon,
                        )
                      : null,
                  suffixIcon: widget.suffixIcon != null
                      ? Padding(
                          padding: const EdgeInsets.only(right: DesignTokens.spaceMd),
                          child: widget.suffixIcon,
                        )
                      : null,
                  filled: true,
                  fillColor: widget.enabled 
                      ? DesignTokens.neutral50 
                      : DesignTokens.neutral100,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(
                      color: _hasError 
                          ? DesignTokens.accentRed 
                          : DesignTokens.neutral300,
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(
                      color: _hasError 
                          ? DesignTokens.accentRed 
                          : DesignTokens.neutral300,
                      width: 1.5,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(
                      color: _hasError 
                          ? DesignTokens.accentRed 
                          : _borderColorAnimation.value ?? DesignTokens.primaryBlue,
                      width: 2.5,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: const BorderSide(
                      color: DesignTokens.accentRed,
                      width: 2,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: const BorderSide(
                      color: DesignTokens.accentRed,
                      width: 2.5,
                    ),
                  ),
                  contentPadding: widget.contentPadding ?? 
                      const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spaceLg + 4,
                        vertical: DesignTokens.spaceLg + 2,
                      ),
                  labelStyle: theme.textTheme.bodyMedium?.copyWith(
                    color: _isFocused 
                        ? DesignTokens.primaryBlue 
                        : DesignTokens.textTertiary,
                    fontWeight: FontWeight.w500,
                  ),
                  hintStyle: theme.textTheme.bodyMedium?.copyWith(
                    color: DesignTokens.textQuaternary,
                  ),
                  helperStyle: theme.textTheme.bodySmall?.copyWith(
                    color: DesignTokens.textTertiary,
                  ),
                  errorStyle: theme.textTheme.bodySmall?.copyWith(
                    color: DesignTokens.accentRed,
                    fontWeight: FontWeight.w500,
                  ),
                  floatingLabelStyle: theme.textTheme.bodySmall?.copyWith(
                    color: _isFocused 
                        ? DesignTokens.primaryBlue 
                        : DesignTokens.textTertiary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
