import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../blocs/auth/auth_bloc.dart';
import '../../utils/design_tokens.dart';
import '../common/modern_button.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _obscurePassword = true;

  void _handleLogin() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      
      context.read<AuthBloc>().add(
        AuthSignInRequested(
          email: formData['email'],
          password: formData['password'],
        ),
      );
    }
  }

  void _handleForgotPassword() {
    if (_formKey.currentState?.fields['email']?.value != null &&
        _formKey.currentState!.fields['email']!.value.isNotEmpty) {
      context.read<AuthBloc>().add(
        AuthPasswordResetRequested(
          email: _formKey.currentState!.fields['email']!.value,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter your email address first'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return FormBuilder(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Email Field with modern styling
              FormBuilderTextField(
                name: 'email',
                decoration: InputDecoration(
                  labelText: 'Email Address',
                  hintText: 'Enter your email',
                  prefixIcon: Container(
                    margin: const EdgeInsets.only(right: DesignTokens.spaceMd),
                    child: Icon(
                      Icons.email_outlined,
                      color: DesignTokens.textTertiary,
                      size: 20,
                    ),
                  ),
                  filled: true,
                  fillColor: DesignTokens.neutral50,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(color: DesignTokens.neutral300, width: 1.5),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(color: DesignTokens.neutral300, width: 1.5),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(color: DesignTokens.primaryBlue, width: 2.5),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spaceLg + 4,
                    vertical: DesignTokens.spaceLg + 2,
                  ),
                ),
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.email(),
                ]),
                enabled: !isLoading,
              ),

              const SizedBox(height: DesignTokens.spaceLg + 4),

              // Password Field with modern styling
              FormBuilderTextField(
                name: 'password',
                decoration: InputDecoration(
                  labelText: 'Password',
                  hintText: 'Enter your password',
                  prefixIcon: Container(
                    margin: const EdgeInsets.only(right: DesignTokens.spaceMd),
                    child: Icon(
                      Icons.lock_outlined,
                      color: DesignTokens.textTertiary,
                      size: 20,
                    ),
                  ),
                  suffixIcon: Container(
                    margin: const EdgeInsets.only(left: DesignTokens.spaceMd),
                    child: IconButton(
                      icon: Icon(
                        _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                        color: DesignTokens.textTertiary,
                        size: 20,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                  ),
                  filled: true,
                  fillColor: DesignTokens.neutral50,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(color: DesignTokens.neutral300, width: 1.5),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(color: DesignTokens.neutral300, width: 1.5),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    borderSide: BorderSide(color: DesignTokens.primaryBlue, width: 2.5),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spaceLg + 4,
                    vertical: DesignTokens.spaceLg + 2,
                  ),
                ),
                obscureText: _obscurePassword,
                textInputAction: TextInputAction.done,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.minLength(6),
                ]),
                enabled: !isLoading,
                onSubmitted: (_) => _handleLogin(),
              ),
              
              const SizedBox(height: 8),
              
              // Forgot Password with modern styling
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: isLoading ? null : _handleForgotPassword,
                  style: TextButton.styleFrom(
                    foregroundColor: DesignTokens.primaryBlue,
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spaceMd,
                      vertical: DesignTokens.spaceSm,
                    ),
                  ),
                  child: Text(
                    'Forgot Password?',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: DesignTokens.primaryBlue,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: DesignTokens.space2xl),

              // Modern Login Button
              ModernButton(
                text: 'Sign In',
                onPressed: isLoading ? null : _handleLogin,
                variant: ModernButtonVariant.primary,
                size: ModernButtonSize.large,
                isLoading: isLoading,
                isFullWidth: true,
                icon: isLoading ? null : const Icon(Icons.login_rounded, size: 20),
              ),
            ],
          ),
        );
      },
    );
  }
}
