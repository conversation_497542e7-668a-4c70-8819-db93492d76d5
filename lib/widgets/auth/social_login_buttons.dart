import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:io';
import '../../blocs/auth/auth_bloc.dart';
import '../../utils/design_tokens.dart';

class SocialLoginButtons extends StatelessWidget {
  const SocialLoginButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return Column(
          children: [
            // Google Sign In with modern design
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                boxShadow: DesignTokens.shadowSm,
              ),
              child: OutlinedButton.icon(
                onPressed: isLoading ? null : () {
                  context.read<AuthBloc>().add(AuthGoogleSignInRequested());
                },
                icon: _buildGoogleIcon(),
                label: Text(
                  'Continue with Google',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: DesignTokens.textPrimary,
                    letterSpacing: -0.1,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  backgroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    vertical: DesignTokens.spaceLg + 2,
                    horizontal: DesignTokens.space2xl,
                  ),
                  side: BorderSide(
                    color: DesignTokens.neutral300,
                    width: 1.5,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                  ),
                ),
              ),
            ),

            // Apple Sign In (iOS only) with modern design
            if (Platform.isIOS) ...[
              const SizedBox(height: DesignTokens.spaceLg),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                  boxShadow: DesignTokens.shadowSm,
                ),
                child: OutlinedButton.icon(
                  onPressed: isLoading ? null : () {
                    context.read<AuthBloc>().add(AuthAppleSignInRequested());
                  },
                  icon: Icon(
                    Icons.apple,
                    color: DesignTokens.textPrimary,
                    size: 20,
                  ),
                  label: Text(
                    'Continue with Apple',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: DesignTokens.textPrimary,
                      letterSpacing: -0.1,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: DesignTokens.spaceLg + 2,
                      horizontal: DesignTokens.space2xl,
                    ),
                    side: BorderSide(
                      color: DesignTokens.neutral300,
                      width: 1.5,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                    ),
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 20,
      height: 20,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: NetworkImage(
            'https://developers.google.com/identity/images/g-logo.png',
          ),
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
