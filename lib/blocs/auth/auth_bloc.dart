import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/user_model.dart';
import '../../services/auth_service.dart';
import '../../services/firestore_service.dart';
import '../../utils/firestore_debug.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService;
  final FirestoreService _firestoreService;

  AuthBloc({
    required AuthService authService,
    required FirestoreService firestoreService,
  })  : _authService = authService,
        _firestoreService = firestoreService,
        super(AuthInitial()) {
    
    // Listen to auth state changes
    _authService.authStateChanges.listen((User? user) {
      if (user != null) {
        add(AuthUserChanged(user));
      } else {
        add(AuthSignedOut());
      }
    });

    on<AuthStarted>(_onAuthStarted);
    on<AuthUserChanged>(_onAuthUserChanged);
    on<AuthSignInRequested>(_onAuthSignInRequested);
    on<AuthSignUpRequested>(_onAuthSignUpRequested);
    on<AuthGoogleSignInRequested>(_onAuthGoogleSignInRequested);
    on<AuthAppleSignInRequested>(_onAuthAppleSignInRequested);
    on<AuthSignOutRequested>(_onAuthSignOutRequested);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
    on<AuthEmailVerificationRequested>(_onAuthEmailVerificationRequested);
    on<AuthPasswordUpdateRequested>(_onAuthPasswordUpdateRequested);
    on<AuthDeleteAccountRequested>(_onAuthDeleteAccountRequested);
    on<AuthSignedOut>(_onAuthSignedOut);
  }

  Future<void> _onAuthStarted(AuthStarted event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    final user = _authService.currentUser;
    if (user != null) {
      try {
        final userModel = await _firestoreService.getUser(user.uid);
        if (userModel != null) {
          emit(AuthAuthenticated(user: user, userModel: userModel));
          // Check Firestore configuration after successful authentication
          FirestoreDebug.checkConfiguration();
        } else {
          emit(AuthUnauthenticated());
        }
      } catch (e) {
        emit(AuthError(message: 'Failed to load user data: $e'));
      }
    } else {
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthUserChanged(AuthUserChanged event, Emitter<AuthState> emit) async {
    try {
      final userModel = await _firestoreService.getUser(event.user.uid);
      if (userModel != null) {
        emit(AuthAuthenticated(user: event.user, userModel: userModel));
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: 'Failed to load user data: $e'));
    }
  }

  Future<void> _onAuthSignInRequested(AuthSignInRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    try {
      final result = await _authService.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );
      
      if (result?.user != null) {
        final userModel = await _firestoreService.getUser(result!.user!.uid);
        if (userModel != null) {
          emit(AuthAuthenticated(user: result.user!, userModel: userModel));
        } else {
          emit(AuthError(message: 'User data not found'));
        }
      } else {
        emit(AuthError(message: 'Sign in failed'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthSignUpRequested(AuthSignUpRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    try {
      final result = await _authService.signUpWithEmailAndPassword(
        email: event.email,
        password: event.password,
        displayName: event.displayName,
      );
      
      if (result?.user != null) {
        final userModel = await _firestoreService.getUser(result!.user!.uid);
        if (userModel != null) {
          emit(AuthAuthenticated(user: result.user!, userModel: userModel));
        } else {
          emit(AuthError(message: 'Failed to create user profile'));
        }
      } else {
        emit(AuthError(message: 'Sign up failed'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthGoogleSignInRequested(AuthGoogleSignInRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    try {
      final result = await _authService.signInWithGoogle();
      
      if (result?.user != null) {
        final userModel = await _firestoreService.getUser(result!.user!.uid);
        if (userModel != null) {
          emit(AuthAuthenticated(user: result.user!, userModel: userModel));
        } else {
          emit(AuthError(message: 'Failed to load user profile'));
        }
      } else {
        emit(AuthUnauthenticated()); // User cancelled
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthAppleSignInRequested(AuthAppleSignInRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    try {
      final result = await _authService.signInWithApple();
      
      if (result?.user != null) {
        final userModel = await _firestoreService.getUser(result!.user!.uid);
        if (userModel != null) {
          emit(AuthAuthenticated(user: result.user!, userModel: userModel));
        } else {
          emit(AuthError(message: 'Failed to load user profile'));
        }
      } else {
        emit(AuthUnauthenticated()); // User cancelled
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthSignOutRequested(AuthSignOutRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    try {
      await _authService.signOut();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthPasswordResetRequested(AuthPasswordResetRequested event, Emitter<AuthState> emit) async {
    try {
      await _authService.sendPasswordResetEmail(event.email);
      emit(AuthPasswordResetSent(email: event.email));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthEmailVerificationRequested(AuthEmailVerificationRequested event, Emitter<AuthState> emit) async {
    try {
      await _authService.sendEmailVerification();
      emit(AuthEmailVerificationSent());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthPasswordUpdateRequested(AuthPasswordUpdateRequested event, Emitter<AuthState> emit) async {
    try {
      await _authService.updatePassword(event.newPassword);
      emit(AuthPasswordUpdated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthDeleteAccountRequested(AuthDeleteAccountRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    
    try {
      await _authService.deleteAccount();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthSignedOut(AuthSignedOut event, Emitter<AuthState> emit) async {
    emit(AuthUnauthenticated());
  }
}
