part of 'resume_bloc.dart';

abstract class ResumeEvent extends Equatable {
  const ResumeEvent();

  @override
  List<Object?> get props => [];
}

class ResumeLoadRequested extends ResumeEvent {
  final String resumeId;

  const ResumeLoadRequested({required this.resumeId});

  @override
  List<Object> get props => [resumeId];
}

class ResumeCreateRequested extends ResumeEvent {
  final ResumeModel resume;

  const ResumeCreateRequested({required this.resume});

  @override
  List<Object> get props => [resume];
}

class ResumeUpdateRequested extends ResumeEvent {
  final ResumeModel resume;

  const ResumeUpdateRequested({required this.resume});

  @override
  List<Object> get props => [resume];
}

class ResumeDeleteRequested extends ResumeEvent {
  final String resumeId;

  const ResumeDeleteRequested({required this.resumeId});

  @override
  List<Object> get props => [resumeId];
}

class ResumeListLoadRequested extends ResumeEvent {
  final String userId;

  const ResumeListLoadRequested({required this.userId});

  @override
  List<Object> get props => [userId];
}

class ResumeGenerateRequested extends ResumeEvent {
  final ResumeModel resume;
  final ResumeStyle style;
  final String? targetIndustry;
  final String? targetJobTitle;

  const ResumeGenerateRequested({
    required this.resume,
    required this.style,
    this.targetIndustry,
    this.targetJobTitle,
  });

  @override
  List<Object?> get props => [resume, style, targetIndustry, targetJobTitle];
}

class ResumeSummaryGenerateRequested extends ResumeEvent {
  final PersonalInfo personalInfo;
  final List<WorkExperience> workExperience;
  final List<Education> education;
  final List<Skill> skills;
  final String? targetIndustry;
  final String? targetJobTitle;

  const ResumeSummaryGenerateRequested({
    required this.personalInfo,
    required this.workExperience,
    required this.education,
    required this.skills,
    this.targetIndustry,
    this.targetJobTitle,
  });

  @override
  List<Object?> get props => [
        personalInfo,
        workExperience,
        education,
        skills,
        targetIndustry,
        targetJobTitle,
      ];
}

class ResumeSearchRequested extends ResumeEvent {
  final String userId;
  final String query;

  const ResumeSearchRequested({
    required this.userId,
    required this.query,
  });

  @override
  List<Object> get props => [userId, query];
}

class ResumeFilterByStyleRequested extends ResumeEvent {
  final String userId;
  final String style;

  const ResumeFilterByStyleRequested({
    required this.userId,
    required this.style,
  });

  @override
  List<Object> get props => [userId, style];
}

class ResumeVersionsLoadRequested extends ResumeEvent {
  final String baseResumeId;

  const ResumeVersionsLoadRequested({required this.baseResumeId});

  @override
  List<Object> get props => [baseResumeId];
}

class ResumeDuplicateRequested extends ResumeEvent {
  final ResumeModel resume;

  const ResumeDuplicateRequested({required this.resume});

  @override
  List<Object> get props => [resume];
}
