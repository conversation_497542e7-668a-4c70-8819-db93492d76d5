import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../models/resume_model.dart';
import '../../models/work_experience_model.dart';
import '../../models/education_model.dart';
import '../../models/skill_model.dart';
import '../../services/firestore_service.dart';
import '../../services/gemini_service.dart';

part 'resume_event.dart';
part 'resume_state.dart';

class ResumeBloc extends Bloc<ResumeEvent, ResumeState> {
  final FirestoreService _firestoreService;
  final GeminiService _geminiService;

  ResumeBloc({
    required FirestoreService firestoreService,
    required GeminiService geminiService,
  })  : _firestoreService = firestoreService,
        _geminiService = geminiService,
        super(ResumeInitial()) {
    
    on<ResumeLoadRequested>(_onResumeLoadRequested);
    on<ResumeCreateRequested>(_onResumeCreateRequested);
    on<ResumeUpdateRequested>(_onResumeUpdateRequested);
    on<ResumeDeleteRequested>(_onResumeDeleteRequested);
    on<ResumeListLoadRequested>(_onResumeListLoadRequested);
    on<ResumeGenerateRequested>(_onResumeGenerateRequested);
    on<ResumeSummaryGenerateRequested>(_onResumeSummaryGenerateRequested);
    on<ResumeSearchRequested>(_onResumeSearchRequested);
    on<ResumeFilterByStyleRequested>(_onResumeFilterByStyleRequested);
    on<ResumeVersionsLoadRequested>(_onResumeVersionsLoadRequested);
    on<ResumeDuplicateRequested>(_onResumeDuplicateRequested);
  }

  Future<void> _onResumeLoadRequested(ResumeLoadRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final resume = await _firestoreService.getResume(event.resumeId);
      if (resume != null) {
        emit(ResumeLoaded(resume: resume));
      } else {
        emit(ResumeError(message: 'Resume not found'));
      }
    } catch (e) {
      emit(ResumeError(message: 'Failed to load resume: $e'));
    }
  }

  Future<void> _onResumeCreateRequested(ResumeCreateRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final resumeId = await _firestoreService.createResume(event.resume);
      final createdResume = event.resume.copyWith(id: resumeId);
      emit(ResumeCreated(resume: createdResume));
    } catch (e) {
      emit(ResumeError(message: 'Failed to create resume: $e'));
    }
  }

  Future<void> _onResumeUpdateRequested(ResumeUpdateRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      await _firestoreService.updateResume(event.resume);
      emit(ResumeUpdated(resume: event.resume));
    } catch (e) {
      emit(ResumeError(message: 'Failed to update resume: $e'));
    }
  }

  Future<void> _onResumeDeleteRequested(ResumeDeleteRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      await _firestoreService.deleteResume(event.resumeId);
      emit(ResumeDeleted(resumeId: event.resumeId));
    } catch (e) {
      emit(ResumeError(message: 'Failed to delete resume: $e'));
    }
  }

  Future<void> _onResumeListLoadRequested(ResumeListLoadRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final resumes = await _firestoreService.getUserResumes(event.userId);
      emit(ResumeListLoaded(resumes: resumes));
    } catch (e) {
      emit(ResumeError(message: 'Failed to load resumes: $e'));
    }
  }

  Future<void> _onResumeGenerateRequested(ResumeGenerateRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeGenerating());
    
    try {
      final generatedContent = await _geminiService.generateResumeContent(
        resume: event.resume,
        style: event.style,
        targetIndustry: event.targetIndustry,
        targetJobTitle: event.targetJobTitle,
      );
      
      emit(ResumeGenerated(
        originalResume: event.resume,
        generatedContent: generatedContent,
        style: event.style,
      ));
    } catch (e) {
      emit(ResumeError(message: 'Failed to generate resume: $e'));
    }
  }

  Future<void> _onResumeSummaryGenerateRequested(ResumeSummaryGenerateRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeGenerating());
    
    try {
      final generatedSummary = await _geminiService.generateSummary(
        personalInfo: event.personalInfo,
        workExperience: event.workExperience,
        education: event.education,
        skills: event.skills,
        targetIndustry: event.targetIndustry,
        targetJobTitle: event.targetJobTitle,
      );
      
      emit(ResumeSummaryGenerated(summary: generatedSummary));
    } catch (e) {
      emit(ResumeError(message: 'Failed to generate summary: $e'));
    }
  }

  Future<void> _onResumeSearchRequested(ResumeSearchRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final resumes = await _firestoreService.searchResumes(event.userId, event.query);
      emit(ResumeSearchResults(resumes: resumes, query: event.query));
    } catch (e) {
      emit(ResumeError(message: 'Failed to search resumes: $e'));
    }
  }

  Future<void> _onResumeFilterByStyleRequested(ResumeFilterByStyleRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final resumes = await _firestoreService.getResumesByStyle(event.userId, event.style);
      emit(ResumeFilteredByStyle(resumes: resumes, style: event.style));
    } catch (e) {
      emit(ResumeError(message: 'Failed to filter resumes: $e'));
    }
  }

  Future<void> _onResumeVersionsLoadRequested(ResumeVersionsLoadRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final versions = await _firestoreService.getResumeVersions(event.baseResumeId);
      emit(ResumeVersionsLoaded(versions: versions));
    } catch (e) {
      emit(ResumeError(message: 'Failed to load resume versions: $e'));
    }
  }

  Future<void> _onResumeDuplicateRequested(ResumeDuplicateRequested event, Emitter<ResumeState> emit) async {
    emit(ResumeLoading());
    
    try {
      final duplicatedResume = event.resume.copyWith(
        id: '', // Will be set by Firestore
        name: '${event.resume.name} (Copy)',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        version: 1,
        pdfUrl: null,
        shareUrl: null,
      );
      
      final resumeId = await _firestoreService.createResume(duplicatedResume);
      final finalResume = duplicatedResume.copyWith(id: resumeId);
      
      emit(ResumeDuplicated(resume: finalResume));
    } catch (e) {
      emit(ResumeError(message: 'Failed to duplicate resume: $e'));
    }
  }
}
