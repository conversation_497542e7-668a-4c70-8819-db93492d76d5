part of 'resume_bloc.dart';

abstract class ResumeState extends Equatable {
  const ResumeState();

  @override
  List<Object?> get props => [];
}

class ResumeInitial extends ResumeState {}

class ResumeLoading extends ResumeState {}

class ResumeGenerating extends ResumeState {}

class ResumeLoaded extends ResumeState {
  final ResumeModel resume;

  const ResumeLoaded({required this.resume});

  @override
  List<Object> get props => [resume];
}

class ResumeCreated extends ResumeState {
  final ResumeModel resume;

  const ResumeCreated({required this.resume});

  @override
  List<Object> get props => [resume];
}

class ResumeUpdated extends ResumeState {
  final ResumeModel resume;

  const ResumeUpdated({required this.resume});

  @override
  List<Object> get props => [resume];
}

class ResumeDeleted extends ResumeState {
  final String resumeId;

  const ResumeDeleted({required this.resumeId});

  @override
  List<Object> get props => [resumeId];
}

class ResumeListLoaded extends ResumeState {
  final List<ResumeModel> resumes;

  const ResumeListLoaded({required this.resumes});

  @override
  List<Object> get props => [resumes];
}

class ResumeGenerated extends ResumeState {
  final ResumeModel originalResume;
  final String generatedContent;
  final ResumeStyle style;

  const ResumeGenerated({
    required this.originalResume,
    required this.generatedContent,
    required this.style,
  });

  @override
  List<Object> get props => [originalResume, generatedContent, style];
}

class ResumeSummaryGenerated extends ResumeState {
  final String summary;

  const ResumeSummaryGenerated({required this.summary});

  @override
  List<Object> get props => [summary];
}

class ResumeSearchResults extends ResumeState {
  final List<ResumeModel> resumes;
  final String query;

  const ResumeSearchResults({
    required this.resumes,
    required this.query,
  });

  @override
  List<Object> get props => [resumes, query];
}

class ResumeFilteredByStyle extends ResumeState {
  final List<ResumeModel> resumes;
  final String style;

  const ResumeFilteredByStyle({
    required this.resumes,
    required this.style,
  });

  @override
  List<Object> get props => [resumes, style];
}

class ResumeVersionsLoaded extends ResumeState {
  final List<ResumeModel> versions;

  const ResumeVersionsLoaded({required this.versions});

  @override
  List<Object> get props => [versions];
}

class ResumeDuplicated extends ResumeState {
  final ResumeModel resume;

  const ResumeDuplicated({required this.resume});

  @override
  List<Object> get props => [resume];
}

class ResumeError extends ResumeState {
  final String message;

  const ResumeError({required this.message});

  @override
  List<Object> get props => [message];
}
