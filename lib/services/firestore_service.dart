import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/resume_model.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String usersCollection = 'users';
  static const String resumesCollection = 'resumes';

  // User operations
  Future<void> createUser(UserModel user) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(user.id)
          .set(user.toJson());
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: Cannot create user. Please check Firestore security rules.');
      } else if (e.code == 'unavailable') {
        throw Exception('Firestore service unavailable. Please enable Firestore API.');
      }
      throw Exception('Firestore error: ${e.message}');
    } catch (e) {
      throw Exception('Error creating user: $e');
    }
  }

  Future<UserModel?> getUser(String userId) async {
    try {
      final doc = await _firestore
          .collection(usersCollection)
          .doc(userId)
          .get();

      if (doc.exists && doc.data() != null) {
        return UserModel.fromJson(doc.data()!);
      }
      return null;
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Firestore access denied. Please check your security rules and ensure Firestore is enabled.');
      } else if (e.code == 'unavailable') {
        throw Exception('Firestore service is currently unavailable. Please try again later.');
      }
      throw Exception('Firestore error: ${e.message}');
    } catch (e) {
      throw Exception('Error getting user: $e');
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(user.id)
          .update(user.toJson());
    } catch (e) {
      throw Exception('Error updating user: $e');
    }
  }

  Future<void> updateUserLastLogin(String userId, DateTime lastLogin) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update({'lastLoginAt': lastLogin.toIso8601String()});
    } catch (e) {
      throw Exception('Error updating last login: $e');
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      // Delete all user's resumes first
      final resumes = await getUserResumes(userId);
      for (final resume in resumes) {
        await deleteResume(resume.id);
      }

      // Delete user document
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .delete();
    } catch (e) {
      throw Exception('Error deleting user: $e');
    }
  }

  // Resume operations
  Future<String> createResume(ResumeModel resume) async {
    try {
      final docRef = await _firestore
          .collection(resumesCollection)
          .add(resume.toJson());

      // Update the resume with the generated ID
      final updatedResume = resume.copyWith(id: docRef.id);
      await docRef.update(updatedResume.toJson());

      // Add resume ID to user's resume list
      await _addResumeToUser(resume.userId, docRef.id);

      return docRef.id;
    } catch (e) {
      throw Exception('Error creating resume: $e');
    }
  }

  Future<ResumeModel?> getResume(String resumeId) async {
    try {
      final doc = await _firestore
          .collection(resumesCollection)
          .doc(resumeId)
          .get();

      if (doc.exists && doc.data() != null) {
        return ResumeModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Error getting resume: $e');
    }
  }

  Future<List<ResumeModel>> getUserResumes(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(resumesCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('updatedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ResumeModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Error getting user resumes: $e');
    }
  }

  Future<void> updateResume(ResumeModel resume) async {
    try {
      final updatedResume = resume.copyWith(
        updatedAt: DateTime.now(),
        version: resume.version + 1,
      );

      await _firestore
          .collection(resumesCollection)
          .doc(resume.id)
          .update(updatedResume.toJson());
    } catch (e) {
      throw Exception('Error updating resume: $e');
    }
  }

  Future<void> deleteResume(String resumeId) async {
    try {
      // Get resume to find user ID
      final resume = await getResume(resumeId);
      if (resume != null) {
        // Remove resume ID from user's resume list
        await _removeResumeFromUser(resume.userId, resumeId);
      }

      // Delete resume document
      await _firestore
          .collection(resumesCollection)
          .doc(resumeId)
          .delete();
    } catch (e) {
      throw Exception('Error deleting resume: $e');
    }
  }

  Future<List<ResumeModel>> getResumeVersions(String baseResumeId) async {
    try {
      final querySnapshot = await _firestore
          .collection(resumesCollection)
          .where('baseResumeId', isEqualTo: baseResumeId)
          .orderBy('version', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ResumeModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Error getting resume versions: $e');
    }
  }

  Future<void> updateResumePdfUrl(String resumeId, String pdfUrl) async {
    try {
      await _firestore
          .collection(resumesCollection)
          .doc(resumeId)
          .update({
        'pdfUrl': pdfUrl,
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Error updating resume PDF URL: $e');
    }
  }

  Future<void> updateResumeShareUrl(String resumeId, String shareUrl) async {
    try {
      await _firestore
          .collection(resumesCollection)
          .doc(resumeId)
          .update({
        'shareUrl': shareUrl,
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Error updating resume share URL: $e');
    }
  }

  // Helper methods
  Future<void> _addResumeToUser(String userId, String resumeId) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update({
        'resumeIds': FieldValue.arrayUnion([resumeId]),
      });
    } catch (e) {
      throw Exception('Error adding resume to user: $e');
    }
  }

  Future<void> _removeResumeFromUser(String userId, String resumeId) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update({
        'resumeIds': FieldValue.arrayRemove([resumeId]),
      });
    } catch (e) {
      throw Exception('Error removing resume from user: $e');
    }
  }

  // Search and filter operations
  Future<List<ResumeModel>> searchResumes(String userId, String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(resumesCollection)
          .where('userId', isEqualTo: userId)
          .get();

      final resumes = querySnapshot.docs
          .map((doc) => ResumeModel.fromJson(doc.data()))
          .toList();

      // Filter resumes based on query
      return resumes.where((resume) {
        final searchText = query.toLowerCase();
        return resume.name.toLowerCase().contains(searchText) ||
               resume.personalInfo.fullName.toLowerCase().contains(searchText) ||
               resume.summary.toLowerCase().contains(searchText);
      }).toList();
    } catch (e) {
      throw Exception('Error searching resumes: $e');
    }
  }

  Future<List<ResumeModel>> getResumesByStyle(String userId, String style) async {
    try {
      final querySnapshot = await _firestore
          .collection(resumesCollection)
          .where('userId', isEqualTo: userId)
          .where('style', isEqualTo: style)
          .orderBy('updatedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ResumeModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Error getting resumes by style: $e');
    }
  }

  // Batch operations
  Future<void> batchUpdateResumes(List<ResumeModel> resumes) async {
    try {
      final batch = _firestore.batch();

      for (final resume in resumes) {
        final docRef = _firestore
            .collection(resumesCollection)
            .doc(resume.id);
        batch.update(docRef, resume.toJson());
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Error batch updating resumes: $e');
    }
  }
}
