import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;

class StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Storage paths
  static const String resumePdfsPath = 'resume_pdfs';
  static const String userProfilesPath = 'user_profiles';
  static const String resumeTemplatesPath = 'resume_templates';

  // Upload PDF file
  Future<String> uploadResumePdf({
    required String userId,
    required String resumeId,
    required Uint8List pdfBytes,
    String? fileName,
  }) async {
    try {
      final String finalFileName = fileName ?? 'resume_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final String filePath = '$resumePdfsPath/$userId/$resumeId/$finalFileName';

      final Reference ref = _storage.ref().child(filePath);
      
      final UploadTask uploadTask = ref.putData(
        pdfBytes,
        SettableMetadata(
          contentType: 'application/pdf',
          customMetadata: {
            'userId': userId,
            'resumeId': resumeId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Error uploading PDF: $e');
    }
  }

  // Upload profile picture
  Future<String> uploadProfilePicture({
    required String userId,
    required File imageFile,
  }) async {
    try {
      final String fileName = 'profile_${DateTime.now().millisecondsSinceEpoch}${path.extension(imageFile.path)}';
      final String filePath = '$userProfilesPath/$userId/$fileName';

      final Reference ref = _storage.ref().child(filePath);
      
      final UploadTask uploadTask = ref.putFile(
        imageFile,
        SettableMetadata(
          contentType: _getContentType(imageFile.path),
          customMetadata: {
            'userId': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Error uploading profile picture: $e');
    }
  }

  // Upload resume template
  Future<String> uploadResumeTemplate({
    required String templateId,
    required Uint8List templateBytes,
    required String fileName,
  }) async {
    try {
      final String filePath = '$resumeTemplatesPath/$templateId/$fileName';

      final Reference ref = _storage.ref().child(filePath);
      
      final UploadTask uploadTask = ref.putData(
        templateBytes,
        SettableMetadata(
          contentType: _getContentType(fileName),
          customMetadata: {
            'templateId': templateId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Error uploading template: $e');
    }
  }

  // Download file as bytes
  Future<Uint8List?> downloadFile(String downloadUrl) async {
    try {
      final Reference ref = _storage.refFromURL(downloadUrl);
      final Uint8List? data = await ref.getData();
      return data;
    } catch (e) {
      throw Exception('Error downloading file: $e');
    }
  }

  // Delete file
  Future<void> deleteFile(String downloadUrl) async {
    try {
      final Reference ref = _storage.refFromURL(downloadUrl);
      await ref.delete();
    } catch (e) {
      throw Exception('Error deleting file: $e');
    }
  }

  // Delete all user files
  Future<void> deleteUserFiles(String userId) async {
    try {
      // Delete resume PDFs
      final resumePdfsRef = _storage.ref().child('$resumePdfsPath/$userId');
      await _deleteDirectory(resumePdfsRef);

      // Delete profile pictures
      final profileRef = _storage.ref().child('$userProfilesPath/$userId');
      await _deleteDirectory(profileRef);
    } catch (e) {
      throw Exception('Error deleting user files: $e');
    }
  }

  // Delete resume files
  Future<void> deleteResumeFiles(String userId, String resumeId) async {
    try {
      final resumeRef = _storage.ref().child('$resumePdfsPath/$userId/$resumeId');
      await _deleteDirectory(resumeRef);
    } catch (e) {
      throw Exception('Error deleting resume files: $e');
    }
  }

  // Get file metadata
  Future<FullMetadata> getFileMetadata(String downloadUrl) async {
    try {
      final Reference ref = _storage.refFromURL(downloadUrl);
      return await ref.getMetadata();
    } catch (e) {
      throw Exception('Error getting file metadata: $e');
    }
  }

  // List files in directory
  Future<List<Reference>> listFiles(String path) async {
    try {
      final Reference ref = _storage.ref().child(path);
      final ListResult result = await ref.listAll();
      return result.items;
    } catch (e) {
      throw Exception('Error listing files: $e');
    }
  }

  // Get download URL from path
  Future<String> getDownloadUrl(String path) async {
    try {
      final Reference ref = _storage.ref().child(path);
      return await ref.getDownloadURL();
    } catch (e) {
      throw Exception('Error getting download URL: $e');
    }
  }

  // Upload with progress tracking
  Stream<TaskSnapshot> uploadWithProgress({
    required String path,
    required Uint8List data,
    SettableMetadata? metadata,
  }) {
    final Reference ref = _storage.ref().child(path);
    final UploadTask uploadTask = ref.putData(data, metadata);
    return uploadTask.snapshotEvents;
  }

  // Helper methods
  Future<void> _deleteDirectory(Reference ref) async {
    try {
      final ListResult result = await ref.listAll();
      
      // Delete all files
      for (final Reference fileRef in result.items) {
        await fileRef.delete();
      }
      
      // Recursively delete subdirectories
      for (final Reference dirRef in result.prefixes) {
        await _deleteDirectory(dirRef);
      }
    } catch (e) {
      // Directory might not exist, which is fine
      if (!e.toString().contains('object-not-found')) {
        rethrow;
      }
    }
  }

  String _getContentType(String fileName) {
    final String extension = path.extension(fileName).toLowerCase();
    switch (extension) {
      case '.pdf':
        return 'application/pdf';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.svg':
        return 'image/svg+xml';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }

  // Generate shareable link with expiration
  Future<String> generateShareableLink({
    required String userId,
    required String resumeId,
    required String fileName,
    Duration? expiration,
  }) async {
    try {
      final String filePath = '$resumePdfsPath/$userId/$resumeId/$fileName';
      final Reference ref = _storage.ref().child(filePath);
      
      final DateTime expirationTime = expiration != null 
          ? DateTime.now().add(expiration)
          : DateTime.now().add(const Duration(days: 365)); // Default 1 year
      
      return await ref.getDownloadURL();
    } catch (e) {
      throw Exception('Error generating shareable link: $e');
    }
  }

  // Check if file exists
  Future<bool> fileExists(String path) async {
    try {
      final Reference ref = _storage.ref().child(path);
      await ref.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get file size
  Future<int> getFileSize(String downloadUrl) async {
    try {
      final Reference ref = _storage.refFromURL(downloadUrl);
      final FullMetadata metadata = await ref.getMetadata();
      return metadata.size ?? 0;
    } catch (e) {
      throw Exception('Error getting file size: $e');
    }
  }
}
