import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';

class GoogleSignInDebug {
  static Future<void> checkConfiguration() async {
    if (kDebugMode) {
      print('🔍 Checking Google Sign-In Configuration...');
      
      try {
        final GoogleSignIn googleSignIn = GoogleSignIn();
        
        // Check if Google Sign-In is available
        final bool isAvailable = await googleSignIn.isSignedIn();
        print('✅ Google Sign-In service available: $isAvailable');
        
        // Try to get current user (should be null if not signed in)
        final GoogleSignInAccount? currentUser = googleSignIn.currentUser;
        print('👤 Current user: ${currentUser?.email ?? 'None'}');
        
        // Check if we can initialize sign-in
        try {
          await googleSignIn.signInSilently();
          print('✅ Silent sign-in check completed');
        } catch (e) {
          print('⚠️ Silent sign-in failed: $e');
        }
        
        print('🔍 Configuration check completed');
        
      } catch (e) {
        print('❌ Google Sign-In configuration error: $e');
        print('💡 Possible issues:');
        print('   - Missing OAuth client configuration in Firebase');
        print('   - Incorrect SHA-1 fingerprint');
        print('   - Missing google-services.json');
        print('   - Network connectivity issues');
      }
    }
  }
  
  static void logSignInAttempt() {
    if (kDebugMode) {
      print('🚀 Starting Google Sign-In attempt...');
    }
  }
  
  static void logSignInSuccess(String? email) {
    if (kDebugMode) {
      print('✅ Google Sign-In successful for: ${email ?? 'Unknown user'}');
    }
  }
  
  static void logSignInError(String error) {
    if (kDebugMode) {
      print('❌ Google Sign-In failed: $error');
    }
  }
  
  static void logSignInCancelled() {
    if (kDebugMode) {
      print('⚠️ Google Sign-In cancelled by user');
    }
  }
}
