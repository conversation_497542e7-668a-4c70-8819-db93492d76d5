import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class FirestoreDebug {
  static Future<void> checkConfiguration() async {
    if (kDebugMode) {
      print('🔍 Checking Firestore Configuration...');
      
      try {
        final FirebaseFirestore firestore = FirebaseFirestore.instance;
        final FirebaseAuth auth = FirebaseAuth.instance;
        
        // Check if user is authenticated
        final User? currentUser = auth.currentUser;
        if (currentUser == null) {
          print('⚠️ No authenticated user found');
          return;
        }
        
        print('✅ Authenticated user: ${currentUser.email}');
        
        // Try to read from Firestore
        try {
          final testDoc = await firestore
              .collection('test')
              .doc('connection')
              .get();
          
          print('✅ Firestore connection successful');
          
          // Try to write to Firestore (test user document)
          try {
            await firestore
                .collection('users')
                .doc(currentUser.uid)
                .set({
              'test': true,
              'timestamp': FieldValue.serverTimestamp(),
            }, SetOptions(merge: true));
            
            print('✅ Firestore write test successful');
            
            // Clean up test data
            await firestore
                .collection('users')
                .doc(currentUser.uid)
                .update({'test': FieldValue.delete()});
                
          } catch (e) {
            print('❌ Firestore write test failed: $e');
            _printFirestoreSetupInstructions();
          }
          
        } catch (e) {
          print('❌ Firestore read test failed: $e');
          _printFirestoreSetupInstructions();
        }
        
      } catch (e) {
        print('❌ Firestore configuration error: $e');
        _printFirestoreSetupInstructions();
      }
    }
  }
  
  static void _printFirestoreSetupInstructions() {
    if (kDebugMode) {
      print('');
      print('🛠️ Firestore Setup Instructions:');
      print('1. Go to: https://console.developers.google.com/apis/api/firestore.googleapis.com/overview?project=resume-d24cb');
      print('2. Click "Enable" to enable Cloud Firestore API');
      print('3. Go to Firebase Console: https://console.firebase.google.com/project/resume-d24cb');
      print('4. Navigate to "Firestore Database"');
      print('5. Click "Create database"');
      print('6. Choose "Start in test mode" for development');
      print('7. Select a location close to your users');
      print('8. Wait 2-3 minutes for changes to propagate');
      print('');
    }
  }
  
  static void logFirestoreOperation(String operation, String collection, {String? docId}) {
    if (kDebugMode) {
      final docInfo = docId != null ? '/$docId' : '';
      print('🔥 Firestore $operation: $collection$docInfo');
    }
  }
  
  static void logFirestoreError(String operation, String error) {
    if (kDebugMode) {
      print('❌ Firestore $operation failed: $error');
    }
  }
  
  static void logFirestoreSuccess(String operation) {
    if (kDebugMode) {
      print('✅ Firestore $operation successful');
    }
  }
}
