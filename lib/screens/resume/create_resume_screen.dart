import 'package:flutter/material.dart';
import '../../utils/design_tokens.dart';

class CreateResumeScreen extends StatefulWidget {
  const CreateResumeScreen({super.key});

  @override
  State<CreateResumeScreen> createState() => _CreateResumeScreenState();
}

class _CreateResumeScreenState extends State<CreateResumeScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  final List<String> _stepTitles = [
    'Choose Template',
    'Personal Info',
    'Experience',
    'Review & Generate',
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Resume'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Modern Progress Indicator
          Container(
            padding: const EdgeInsets.all(DesignTokens.spaceLg),
            margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spaceLg),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
              boxShadow: DesignTokens.shadowSm,
              border: Border.all(color: DesignTokens.neutral200),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    for (int i = 0; i < _totalSteps; i++) ...[
                      Expanded(
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: i <= _currentStep
                                ? DesignTokens.primaryBlue
                                : DesignTokens.neutral300,
                            borderRadius: BorderRadius.circular(DesignTokens.radiusXs),
                          ),
                        ),
                      ),
                      if (i < _totalSteps - 1) const SizedBox(width: DesignTokens.spaceSm),
                    ],
                  ],
                ),
                const SizedBox(height: DesignTokens.spaceLg),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spaceMd,
                        vertical: DesignTokens.spaceXs,
                      ),
                      decoration: BoxDecoration(
                        color: DesignTokens.primaryBlue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(DesignTokens.radiusSm),
                      ),
                      child: Text(
                        'Step ${_currentStep + 1} of $_totalSteps',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: DesignTokens.primaryBlue,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    Text(
                      _stepTitles[_currentStep],
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: DesignTokens.textPrimary,
                        letterSpacing: -0.1,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Step Content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildTemplateStep(),
                _buildPersonalInfoStep(),
                _buildExperienceStep(),
                _buildReviewStep(),
              ],
            ),
          ),
          
          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousStep,
                      child: const Text('Previous'),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentStep < _totalSteps - 1 ? _nextStep : _generateResume,
                    child: Text(_currentStep < _totalSteps - 1 ? 'Next' : 'Generate Resume'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateStep() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose a Template',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a style that best fits your industry and personal preference',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildTemplateCard(
                  'Professional',
                  'Clean and traditional format',
                  Icons.business_center_outlined,
                  Colors.blue,
                ),
                _buildTemplateCard(
                  'Creative',
                  'Eye-catching design',
                  Icons.palette_outlined,
                  Colors.purple,
                ),
                _buildTemplateCard(
                  'Minimalist',
                  'Simple and elegant',
                  Icons.minimize_outlined,
                  Colors.grey,
                ),
                _buildTemplateCard(
                  'Modern',
                  'Contemporary design',
                  Icons.auto_awesome_outlined,
                  Colors.teal,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(String title, String description, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          // TODO: Select template
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 32, color: color),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: Text('Personal Info Step - Coming Soon'),
      ),
    );
  }

  Widget _buildExperienceStep() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: Text('Experience Step - Coming Soon'),
      ),
    );
  }

  Widget _buildReviewStep() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: Text('Review Step - Coming Soon'),
      ),
    );
  }

  void _generateResume() {
    // TODO: Implement resume generation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Resume generation coming soon!'),
      ),
    );
  }
}
